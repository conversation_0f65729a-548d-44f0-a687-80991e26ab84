<?php

namespace Tests\Feature\Api;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class UserTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // Create and authenticate a user for most tests
        $this->user = User::factory()->admin()->create();
        Sanctum::actingAs($this->user);
    }

    /**
     * Test authenticated user can get paginated list of users
     */
    public function test_can_get_paginated_users(): void
    {
        // Create additional users
        User::factory(15)->create();

        $response = $this->getJson('/api/users');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'email',
                            'role',
                            'created_at',
                            'updated_at'
                        ]
                    ],
                    'meta' => [
                        'total',
                        'count',
                        'per_page',
                        'current_page',
                        'total_pages'
                    ],
                    'links'
                ]);
    }

    /**
     * Test can filter users by role
     */
    public function test_can_filter_users_by_role(): void
    {
        User::factory(5)->admin()->create();
        User::factory(3)->manager()->create();
        User::factory(7)->user()->create();

        $response = $this->getJson('/api/users?role=admin');

        $response->assertStatus(200);

        $users = $response->json('data');
        foreach ($users as $user) {
            $this->assertEquals(User::ROLE_ADMIN, $user['role']);
        }
    }

    /**
     * Test can search users by name or email
     */
    public function test_can_search_users(): void
    {
        User::factory()->create(['name' => 'John Doe', 'email' => '<EMAIL>']);
        User::factory()->create(['name' => 'Jane Smith', 'email' => '<EMAIL>']);

        $response = $this->getJson('/api/users?search=John');

        $response->assertStatus(200);

        $users = $response->json('data');
        $this->assertCount(1, $users);
        $this->assertEquals('John Doe', $users[0]['name']);
    }

    /**
     * Test can create a new user
     */
    public function test_can_create_user(): void
    {
        $userData = [
            'name' => $this->faker->name,
            'email' => $this->faker->unique()->safeEmail,
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => User::ROLE_USER,
        ];

        $response = $this->postJson('/api/users', $userData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'message',
                    'data' => [
                        'id',
                        'name',
                        'email',
                        'role'
                    ]
                ]);

        $this->assertDatabaseHas('users', [
            'email' => $userData['email'],
            'name' => $userData['name'],
            'role' => $userData['role'],
        ]);
    }

    /**
     * Test user creation validation
     */
    public function test_user_creation_validation(): void
    {
        $response = $this->postJson('/api/users', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['name', 'email', 'password', 'role']);
    }

    /**
     * Test can show specific user
     */
    public function test_can_show_user(): void
    {
        $user = User::factory()->create();

        $response = $this->getJson("/api/users/{$user->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'message',
                    'data' => [
                        'id',
                        'name',
                        'email',
                        'role'
                    ]
                ])
                ->assertJson([
                    'data' => [
                        'id' => $user->id,
                        'email' => $user->email
                    ]
                ]);
    }

    /**
     * Test returns 404 for non-existent user
     */
    public function test_returns_404_for_non_existent_user(): void
    {
        $response = $this->getJson('/api/users/999999');

        $response->assertStatus(404)
                ->assertJson(['message' => 'User not found']);
    }

    /**
     * Test can update user
     */
    public function test_can_update_user(): void
    {
        $user = User::factory()->create();

        $updateData = [
            'name' => 'Updated Name',
            'role' => User::ROLE_MANAGER,
        ];

        $response = $this->putJson("/api/users/{$user->id}", $updateData);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'message',
                    'data' => [
                        'id',
                        'name',
                        'email',
                        'role'
                    ]
                ]);

        $this->assertDatabaseHas('users', [
            'id' => $user->id,
            'name' => 'Updated Name',
            'role' => User::ROLE_MANAGER,
        ]);
    }

    /**
     * Test can soft delete user
     */
    public function test_can_delete_user(): void
    {
        $user = User::factory()->create();

        $response = $this->deleteJson("/api/users/{$user->id}");

        $response->assertStatus(200)
                ->assertJson(['message' => 'User deleted successfully']);

        $this->assertSoftDeleted('users', ['id' => $user->id]);
    }

    /**
     * Test can restore soft deleted user
     */
    public function test_can_restore_user(): void
    {
        $user = User::factory()->create();
        $user->delete(); // Soft delete

        $response = $this->postJson("/api/users/{$user->id}/restore");

        $response->assertStatus(200)
                ->assertJson(['message' => 'User restored successfully']);

        $this->assertDatabaseHas('users', [
            'id' => $user->id,
            'deleted_at' => null,
        ]);
    }

    /**
     * Test can get user statistics
     */
    public function test_can_get_user_statistics(): void
    {
        User::factory(5)->admin()->create();
        User::factory(3)->manager()->create();
        User::factory(7)->user()->create();

        $response = $this->getJson('/api/users/statistics/overview');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'message',
                    'data' => [
                        'total_users',
                        'active_users',
                        'deleted_users',
                        'users_by_role' => [
                            'admin',
                            'manager',
                            'user'
                        ]
                    ]
                ]);
    }

    /**
     * Test unauthenticated user cannot access user endpoints
     */
    public function test_unauthenticated_user_cannot_access_user_endpoints(): void
    {
        // Create a new test case without authentication
        $this->refreshApplication();

        $response = $this->getJson('/api/users');
        $response->assertStatus(401);

        $response = $this->postJson('/api/users', []);
        $response->assertStatus(401);
    }

    /**
     * Test pagination parameters
     */
    public function test_pagination_parameters(): void
    {
        User::factory(25)->create();

        $response = $this->getJson('/api/users?per_page=5');

        $response->assertStatus(200);

        $meta = $response->json('meta');
        $this->assertEquals(5, $meta['per_page']);
        $this->assertLessThanOrEqual(5, $meta['count']);
    }

    /**
     * Test email uniqueness validation on update
     */
    public function test_email_uniqueness_validation_on_update(): void
    {
        $user1 = User::factory()->create(['email' => '<EMAIL>']);
        $user2 = User::factory()->create(['email' => '<EMAIL>']);

        $response = $this->putJson("/api/users/{$user2->id}", [
            'email' => '<EMAIL>', // Try to use existing email
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['email']);
    }

    /**
     * Test can update user with same email (should not trigger uniqueness error)
     */
    public function test_can_update_user_with_same_email(): void
    {
        $user = User::factory()->create(['email' => '<EMAIL>']);

        $response = $this->putJson("/api/users/{$user->id}", [
            'name' => 'Updated Name',
            'email' => '<EMAIL>', // Same email
        ]);

        $response->assertStatus(200);
    }
}
