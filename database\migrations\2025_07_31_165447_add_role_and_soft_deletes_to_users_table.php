<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add role field with default value 'user'
            $table->enum('role', ['admin', 'manager', 'user'])->default('user')->after('email');

            // Add soft deletes functionality
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Remove soft deletes
            $table->dropSoftDeletes();

            // Remove role field
            $table->dropColumn('role');
        });
    }
};
