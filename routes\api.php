<?php

use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\UserController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public authentication routes
Route::prefix('auth')->group(function () {
    Route::post('login', [AuthController::class, 'login']);
    Route::post('register', [AuthController::class, 'register']);
});

// Protected routes that require authentication
Route::middleware('auth:sanctum')->group(function () {
    
    // Authentication routes
    Route::prefix('auth')->group(function () {
        Route::post('logout', [AuthController::class, 'logout']);
        Route::get('user', [AuthController::class, 'user']);
        Route::post('refresh', [AuthController::class, 'refresh']);
    });

    // User management routes
    Route::prefix('users')->group(function () {
        // Standard resource routes
        Route::get('/', [UserController::class, 'index']);           // GET /api/users
        Route::post('/', [UserController::class, 'store']);          // POST /api/users
        Route::get('/{id}', [UserController::class, 'show']);        // GET /api/users/{id}
        Route::put('/{id}', [UserController::class, 'update']);      // PUT /api/users/{id}
        Route::patch('/{id}', [UserController::class, 'update']);    // PATCH /api/users/{id}
        Route::delete('/{id}', [UserController::class, 'destroy']);  // DELETE /api/users/{id}
        
        // Additional user management routes
        Route::get('/statistics/overview', [UserController::class, 'statistics']); // GET /api/users/statistics/overview
        Route::post('/{id}/restore', [UserController::class, 'restore']);          // POST /api/users/{id}/restore
    });

    // Health check route for authenticated users
    Route::get('/health', function (Request $request) {
        return response()->json([
            'status' => 'ok',
            'message' => 'API is working',
            'user' => $request->user()->only(['id', 'name', 'email', 'role']),
            'timestamp' => now()->toISOString(),
        ]);
    });
});

// Public health check route
Route::get('/health/public', function () {
    return response()->json([
        'status' => 'ok',
        'message' => 'API is working',
        'timestamp' => now()->toISOString(),
    ]);
});

// Fallback route for undefined API endpoints
Route::fallback(function () {
    return response()->json([
        'message' => 'API endpoint not found',
        'error' => 'The requested API endpoint does not exist'
    ], 404);
});
