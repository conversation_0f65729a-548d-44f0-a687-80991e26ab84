<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create specific admin user
        User::factory()->create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => User::ROLE_ADMIN,
        ]);

        // Create specific manager user
        User::factory()->create([
            'name' => 'Manager User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => User::ROLE_MANAGER,
        ]);

        // Create specific regular user
        User::factory()->create([
            'name' => 'Regular User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => User::ROLE_USER,
        ]);

        // Create additional random users
        User::factory(5)->admin()->create();
        User::factory(10)->manager()->create();
        User::factory(20)->user()->create();
    }
}
