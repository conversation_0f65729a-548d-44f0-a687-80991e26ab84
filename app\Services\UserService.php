<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Hash;

/**
 * User Service Class
 * 
 * Handles business logic for user operations following clean architecture principles.
 * This service layer separates business logic from controllers and provides
 * reusable methods for user management operations.
 */
class UserService
{
    /**
     * Get paginated list of users
     *
     * @param int $perPage Number of users per page
     * @param array $filters Optional filters (role, search, etc.)
     * @return LengthAwarePaginator
     */
    public function getPaginatedUsers(int $perPage = 15, array $filters = []): LengthAwarePaginator
    {
        $query = User::query();

        // Apply role filter if provided
        if (!empty($filters['role']) && in_array($filters['role'], User::ROLES)) {
            $query->byRole($filters['role']);
        }

        // Apply search filter if provided
        if (!empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Apply active filter (exclude soft deleted users by default)
        if (!isset($filters['include_deleted']) || !$filters['include_deleted']) {
            $query->active();
        }

        // Order by creation date (newest first)
        $query->orderBy('created_at', 'desc');

        return $query->paginate($perPage);
    }

    /**
     * Get all users without pagination
     *
     * @param array $filters Optional filters
     * @return Collection
     */
    public function getAllUsers(array $filters = []): Collection
    {
        $query = User::query();

        // Apply role filter if provided
        if (!empty($filters['role']) && in_array($filters['role'], User::ROLES)) {
            $query->byRole($filters['role']);
        }

        // Apply active filter (exclude soft deleted users by default)
        if (!isset($filters['include_deleted']) || !$filters['include_deleted']) {
            $query->active();
        }

        return $query->get();
    }

    /**
     * Find user by ID
     *
     * @param int $id User ID
     * @param bool $includeDeleted Whether to include soft deleted users
     * @return User|null
     */
    public function findUser(int $id, bool $includeDeleted = false): ?User
    {
        $query = User::query();

        if ($includeDeleted) {
            $query->withTrashed();
        }

        return $query->find($id);
    }

    /**
     * Create a new user
     *
     * @param array $data User data
     * @return User
     */
    public function createUser(array $data): User
    {
        // Hash password if provided
        if (isset($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        }

        // Set default role if not provided
        if (!isset($data['role'])) {
            $data['role'] = User::ROLE_USER;
        }

        return User::create($data);
    }

    /**
     * Update an existing user
     *
     * @param User $user User instance to update
     * @param array $data Updated user data
     * @return User
     */
    public function updateUser(User $user, array $data): User
    {
        // Hash password if provided
        if (isset($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        }

        $user->update($data);
        
        return $user->fresh();
    }

    /**
     * Soft delete a user
     *
     * @param User $user User instance to delete
     * @return bool
     */
    public function deleteUser(User $user): bool
    {
        return $user->delete();
    }

    /**
     * Restore a soft deleted user
     *
     * @param User $user User instance to restore
     * @return bool
     */
    public function restoreUser(User $user): bool
    {
        return $user->restore();
    }

    /**
     * Permanently delete a user
     *
     * @param User $user User instance to permanently delete
     * @return bool
     */
    public function forceDeleteUser(User $user): bool
    {
        return $user->forceDelete();
    }

    /**
     * Get user statistics
     *
     * @return array
     */
    public function getUserStatistics(): array
    {
        return [
            'total_users' => User::count(),
            'active_users' => User::active()->count(),
            'deleted_users' => User::onlyTrashed()->count(),
            'users_by_role' => [
                'admin' => User::byRole(User::ROLE_ADMIN)->count(),
                'manager' => User::byRole(User::ROLE_MANAGER)->count(),
                'user' => User::byRole(User::ROLE_USER)->count(),
            ],
        ];
    }

    /**
     * Check if email is unique (excluding specific user ID)
     *
     * @param string $email Email to check
     * @param int|null $excludeUserId User ID to exclude from check
     * @return bool
     */
    public function isEmailUnique(string $email, ?int $excludeUserId = null): bool
    {
        $query = User::where('email', $email);

        if ($excludeUserId) {
            $query->where('id', '!=', $excludeUserId);
        }

        return !$query->exists();
    }

    /**
     * Bulk update user roles
     *
     * @param array $userIds Array of user IDs
     * @param string $role New role to assign
     * @return int Number of updated users
     */
    public function bulkUpdateRole(array $userIds, string $role): int
    {
        if (!in_array($role, User::ROLES)) {
            throw new \InvalidArgumentException('Invalid role provided');
        }

        return User::whereIn('id', $userIds)->update(['role' => $role]);
    }

    /**
     * Bulk soft delete users
     *
     * @param array $userIds Array of user IDs
     * @return int Number of deleted users
     */
    public function bulkDeleteUsers(array $userIds): int
    {
        return User::whereIn('id', $userIds)->delete();
    }
}
